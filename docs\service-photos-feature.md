# 订单服务照片查看功能

## 功能概述

用户端小程序现已支持查看订单服务照片功能。当订单状态为"服务中"、"已完成"或"已评价"时，用户可以在订单详情页面查看员工拍摄的服务前后照片。

## 功能特性

### 1. 自动加载
- 当订单状态为"服务中"、"已完成"或"已评价"时，自动调用API获取服务照片
- 如果没有照片数据，不会显示照片区域，也不会显示错误提示

### 2. 分组显示
- **服务前照片**：显示员工开始服务前拍摄的照片（最多9张）
- **服务后照片**：显示员工完成服务后拍摄的照片（最多9张）
- 每组照片都显示拍摄时间

### 3. 图片预览
- 点击任意照片可以进入全屏预览模式
- 支持左右滑动查看同组内的其他照片
- 预览时显示当前照片在组内的位置

### 4. 响应式布局
- 照片以网格形式展示，每行最多显示3张
- 照片尺寸为200rpx × 200rpx，圆角设计
- 适配不同屏幕尺寸

## 技术实现

### API接口
```javascript
// 新增API配置
servicePhotos: "/orders/{orderId}/service-photos"

// 新增API方法
async getServicePhotos(orderId) {
  // 获取订单服务照片
}
```

### 数据结构
```javascript
servicePhotos: {
  id: 1,
  orderId: 456,
  employeeId: 123,
  beforePhotos: ["url1", "url2", "url3"], // 服务前照片数组
  beforePhotoTime: "2024-01-01 10:00:00", // 服务前照片时间
  afterPhotos: ["url4", "url5"], // 服务后照片数组
  afterPhotoTime: "2024-01-01 11:00:00", // 服务后照片时间
  order: { /* 订单信息 */ },
  employee: { /* 员工信息 */ }
}
```

### 页面组件
- 在订单详情页面(`pages/serviceOrder/orderDetail/`)中集成
- 使用条件渲染，只在有照片时显示
- 复用现有的图片预览功能

## 使用说明

### 用户操作流程
1. 进入订单详情页面
2. 如果订单状态为"服务中"、"已完成"或"已评价"，且有服务照片，会自动显示照片区域
3. 照片按"服务前"和"服务后"分组显示
4. 点击任意照片可以全屏预览
5. 在预览模式下可以左右滑动查看同组的其他照片

### 显示条件
- 订单状态必须是"服务中"、"已完成"或"已评价"
- 后端返回的照片数据不为空
- 至少有一组照片（服务前或服务后）包含图片

## 样式设计

### 整体布局
- 照片区域位于订单详情信息下方，操作按钮上方
- 使用白色背景卡片设计，与其他信息区域保持一致
- 圆角边框和轻微阴影效果

### 照片展示
- 每组照片有独立的标题和时间显示
- 照片网格布局，间距16rpx
- 照片圆角12rpx，带有轻微阴影
- 支持aspectFill模式，确保照片不变形

### 交互效果
- 照片点击时有视觉反馈
- 预览模式使用微信原生的图片预览组件
- 流畅的加载和显示动画

## 错误处理

- API调用失败时不显示错误提示（因为没有照片是正常情况）
- 图片加载失败时显示默认占位符
- 网络异常时静默处理，不影响其他功能

## 兼容性

- 兼容微信小程序基础库2.0+
- 支持iOS和Android平台
- 适配不同屏幕尺寸和分辨率
