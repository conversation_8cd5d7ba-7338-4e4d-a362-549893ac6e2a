# 服务照片功能使用示例

## API响应示例

### 成功响应
```json
{
  "success": true,
  "data": {
    "id": 1,
    "orderId": 456,
    "employeeId": 123,
    "beforePhotos": [
      "https://example.com/before-photo1.jpg",
      "https://example.com/before-photo2.jpg",
      "https://example.com/before-photo3.jpg"
    ],
    "beforePhotoTime": "2024-01-01T10:00:00.000Z",
    "afterPhotos": [
      "https://example.com/after-photo1.jpg",
      "https://example.com/after-photo2.jpg"
    ],
    "afterPhotoTime": "2024-01-01T11:00:00.000Z",
    "order": {
      "id": 456,
      "sn": "1704096000001234",
      "status": "已完成"
    },
    "employee": {
      "id": 123,
      "name": "张三",
      "phone": "13800138000"
    }
  }
}
```

### 无照片响应
```json
{
  "success": false,
  "error": "未找到服务照片",
  "statusCode": 404
}
```

## 页面展示效果

### 有服务前后照片（适用于"服务中"、"已完成"、"已评价"状态）
```
┌─────────────────────────────────┐
│ 订单详情信息                      │
├─────────────────────────────────┤
│ 服务前照片          2024-01-01 10:00 │
│ ┌───┐ ┌───┐ ┌───┐              │
│ │ 📷 │ │ 📷 │ │ 📷 │              │
│ └───┘ └───┘ └───┘              │
├─────────────────────────────────┤
│ 服务后照片          2024-01-01 11:00 │
│ ┌───┐ ┌───┐                    │
│ │ 📷 │ │ 📷 │                    │
│ └───┘ └───┘                    │
├─────────────────────────────────┤
│ 操作按钮                        │
└─────────────────────────────────┘
```

### 只有服务前照片
```
┌─────────────────────────────────┐
│ 订单详情信息                      │
├─────────────────────────────────┤
│ 服务前照片          2024-01-01 10:00 │
│ ┌───┐ ┌───┐ ┌───┐              │
│ │ 📷 │ │ 📷 │ │ 📷 │              │
│ └───┘ └───┘ └───┘              │
├─────────────────────────────────┤
│ 操作按钮                        │
└─────────────────────────────────┘
```

### 无照片
```
┌─────────────────────────────────┐
│ 订单详情信息                      │
├─────────────────────────────────┤
│ 操作按钮                        │
└─────────────────────────────────┘
```

## 代码调用示例

### 在订单详情页面中的使用
```javascript
// 加载订单数据时自动检查是否需要加载照片
loadOrders(data) {
  // ... 处理订单数据
  
  // 如果订单状态是服务中、已完成或已评价，加载服务照片
  if (orderDetail.status === '服务中' || orderDetail.status === '已完成' || orderDetail.status === '已评价') {
    this.loadServicePhotos(orderDetail.id);
  }
}

// 加载服务照片
async loadServicePhotos(orderId) {
  try {
    const servicePhotos = await orderApi.getServicePhotos(orderId);
    if (servicePhotos) {
      // 格式化时间并设置数据
      const formattedPhotos = {
        ...servicePhotos,
        beforePhotoTime: servicePhotos.beforePhotoTime ? 
          utils.formatNormalDate(servicePhotos.beforePhotoTime) : null,
        afterPhotoTime: servicePhotos.afterPhotoTime ? 
          utils.formatNormalDate(servicePhotos.afterPhotoTime) : null,
      };
      this.setData({ servicePhotos: formattedPhotos });
    }
  } catch (error) {
    console.error('加载服务照片失败:', error);
    // 静默处理错误，不显示提示
  }
}

// 预览照片
previewServicePhoto(e) {
  const { url, type } = e.currentTarget.dataset;
  const { servicePhotos } = this.data;
  
  if (!servicePhotos || !url || !type) return;
  
  const photos = type === 'before' ? 
    servicePhotos.beforePhotos : servicePhotos.afterPhotos;
  
  if (!photos || photos.length === 0) return;
  
  wx.previewImage({
    current: url,
    urls: photos
  });
}
```

### WXML模板使用
```xml
<!-- 服务照片区域 -->
<view wx:if="{{servicePhotos && ((servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0) || (servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0))}}" class="service-photos">
  
  <!-- 服务前照片 -->
  <view wx:if="{{servicePhotos.beforePhotos && servicePhotos.beforePhotos.length > 0}}" class="photo-section">
    <view class="section-title">
      <text class="title-text">服务前照片</text>
      <text class="photo-time" wx:if="{{servicePhotos.beforePhotoTime}}">{{servicePhotos.beforePhotoTime}}</text>
    </view>
    <view class="photo-grid">
      <view wx:for="{{servicePhotos.beforePhotos}}" wx:key="index" class="photo-item">
        <image 
          src="{{item}}" 
          class="photo-preview" 
          mode="aspectFill" 
          bindtap="previewServicePhoto" 
          data-url="{{item}}"
          data-type="before"
        ></image>
      </view>
    </view>
  </view>

  <!-- 服务后照片 -->
  <view wx:if="{{servicePhotos.afterPhotos && servicePhotos.afterPhotos.length > 0}}" class="photo-section">
    <view class="section-title">
      <text class="title-text">服务后照片</text>
      <text class="photo-time" wx:if="{{servicePhotos.afterPhotoTime}}">{{servicePhotos.afterPhotoTime}}</text>
    </view>
    <view class="photo-grid">
      <view wx:for="{{servicePhotos.afterPhotos}}" wx:key="index" class="photo-item">
        <image 
          src="{{item}}" 
          class="photo-preview" 
          mode="aspectFill" 
          bindtap="previewServicePhoto" 
          data-url="{{item}}"
          data-type="after"
        ></image>
      </view>
    </view>
  </view>
</view>
```

## 测试场景

### 1. 正常场景
- 订单状态为"服务中"，有服务前照片
- 订单状态为"已完成"，有服务前后照片
- 订单状态为"已评价"，有服务前后照片
- 点击照片能正常预览

### 2. 边界场景
- 订单状态为"服务中"，无照片数据
- 订单状态为"已完成"或"已评价"，只有服务前照片
- 照片数组为空
- 网络请求失败

### 3. 异常场景
- API返回404
- 图片链接无效
- 数据格式错误

## 注意事项

1. **性能优化**：只在需要时才加载照片，避免不必要的网络请求
2. **错误处理**：静默处理错误，不影响用户体验
3. **数据安全**：验证所有输入参数，防止异常
4. **用户体验**：使用条件渲染，确保界面流畅
5. **兼容性**：考虑不同设备和网络环境的差异
